#!/usr/bin/env python3
"""
Create a sample point cloud for testing the navigation system.

This script creates a smaller, synthetic point cloud that can be used
for testing and demonstration purposes.
"""

import numpy as np
import open3d as o3d

def create_sample_point_cloud(num_points=10000, save_path="sample_points.pcd"):
    """
    Create a sample RGB point cloud with geometric shapes.
    
    Args:
        num_points: Number of points to generate
        save_path: Path to save the .pcd file
    """
    print(f"Creating sample point cloud with {num_points} points...")
    
    points = []
    colors = []
    
    # Create a cube
    cube_points = num_points // 4
    for i in range(cube_points):
        x = np.random.uniform(-5, 5)
        y = np.random.uniform(-5, 5)
        z = np.random.uniform(-5, 5)
        
        # Color based on position (red cube)
        color = [1.0, 0.2, 0.2]  # Red
        
        points.append([x, y, z])
        colors.append(color)
    
    # Create a sphere
    sphere_points = num_points // 4
    for i in range(sphere_points):
        # Generate random point on sphere
        theta = np.random.uniform(0, 2 * np.pi)
        phi = np.random.uniform(0, np.pi)
        r = np.random.uniform(3, 8)
        
        x = r * np.sin(phi) * np.cos(theta) + 15
        y = r * np.sin(phi) * np.sin(theta)
        z = r * np.cos(phi)
        
        # Color based on radius (blue to green gradient)
        color = [0.2, 0.2 + (r-3)/5 * 0.6, 1.0]  # Blue to cyan
        
        points.append([x, y, z])
        colors.append(color)
    
    # Create a plane
    plane_points = num_points // 4
    for i in range(plane_points):
        x = np.random.uniform(-10, 10)
        y = np.random.uniform(-10, 10)
        z = -8 + np.random.uniform(-0.5, 0.5)  # Slightly noisy plane
        
        # Color based on position (green plane)
        color = [0.2, 1.0, 0.2]  # Green
        
        points.append([x, y, z])
        colors.append(color)
    
    # Create random scattered points
    scatter_points = num_points - cube_points - sphere_points - plane_points
    for i in range(scatter_points):
        x = np.random.uniform(-20, 20)
        y = np.random.uniform(-20, 20)
        z = np.random.uniform(-15, 15)
        
        # Random colors
        color = [np.random.uniform(0.3, 1.0) for _ in range(3)]
        
        points.append([x, y, z])
        colors.append(color)
    
    # Convert to numpy arrays
    points = np.array(points, dtype=np.float32)
    colors = np.array(colors, dtype=np.float32)
    
    # Create Open3D point cloud
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    pcd.colors = o3d.utility.Vector3dVector(colors)
    
    # Save to file
    success = o3d.io.write_point_cloud(save_path, pcd)
    
    if success:
        print(f"✅ Sample point cloud saved to {save_path}")
        print(f"   Points: {len(points)}")
        print(f"   Bounds: X[{points[:, 0].min():.1f}, {points[:, 0].max():.1f}], "
              f"Y[{points[:, 1].min():.1f}, {points[:, 1].max():.1f}], "
              f"Z[{points[:, 2].min():.1f}, {points[:, 2].max():.1f}]")
    else:
        print(f"❌ Failed to save point cloud to {save_path}")
    
    return success

def main():
    """Create sample point clouds of different sizes."""
    print("🎨 Creating sample point clouds for testing...\n")
    
    # Create different sized samples
    samples = [
        (1000, "sample_small.pcd"),
        (10000, "sample_medium.pcd"),
        (50000, "sample_large.pcd"),
    ]
    
    for num_points, filename in samples:
        create_sample_point_cloud(num_points, filename)
        print()
    
    print("✨ Sample point clouds created!")
    print("\nYou can now test the navigator with:")
    print("  python nvs.py sample_small.pcd")
    print("  python nvs.py sample_medium.pcd")
    print("  python nvs.py sample_large.pcd")

if __name__ == "__main__":
    main()
