#!/usr/bin/env python3
"""
Point Cloud Navigation Script

A real-time point cloud viewer with camera navigation controls.
Loads dense RGB point clouds (.pcd format) and provides interactive navigation
with WASD movement and numeric key rotation controls.

Controls:
- W/A/S/D: Camera translation (forward/left/backward/right)
- 1/2: Rotate around X-axis (up/down)
- 3/4: Rotate around Y-axis (left/right)
- 5/6: Rotate around Z-axis (roll left/right)
- ESC/Q: Quit application

Author: AI Assistant
"""

import numpy as np
import cv2
import open3d as o3d
import time
from typing import Tuple, Optional
import argparse
import sys


class PointCloudNavigator:
    """Real-time point cloud navigation with camera controls."""

    def __init__(self, pcd_file: str, window_width: int = 800, window_height: int = 600):
        """
        Initialize the point cloud navigator.

        Args:
            pcd_file: Path to the .pcd file
            window_width: Display window width
            window_height: Display window height
        """
        self.pcd_file = pcd_file
        self.window_width = window_width
        self.window_height = window_height

        # Camera intrinsic parameters (example values - can be adjusted)
        self.fx = 800.0  # Focal length x
        self.fy = 800.0  # Focal length y
        self.cx = window_width / 2.0   # Principal point x
        self.cy = window_height / 2.0  # Principal point y

        # Camera intrinsic matrix
        self.K = np.array([
            [self.fx, 0, self.cx],
            [0, self.fy, self.cy],
            [0, 0, 1]
        ], dtype=np.float32)

        # Camera pose (position and orientation)
        self.camera_position = np.array([0.0, 0.0, 0.0], dtype=np.float32)
        self.camera_rotation = np.eye(3, dtype=np.float32)  # Identity rotation matrix

        # Movement parameters
        self.move_speed = 0.5  # Units per key press
        self.rotation_speed = 0.1  # Radians per key press

        # Point cloud data
        self.points = None
        self.colors = None
        self.point_cloud = None

        # Display window
        self.window_name = "Point Cloud Navigator"

        print("Initializing Point Cloud Navigator...")
        print(f"Controls:")
        print(f"  W/A/S/D: Move forward/left/backward/right")
        print(f"  1/2: Rotate up/down (X-axis)")
        print(f"  3/4: Rotate left/right (Y-axis)")
        print(f"  5/6: Roll left/right (Z-axis)")
        print(f"  ESC/Q: Quit")

    def load_point_cloud(self) -> bool:
        """
        Load the point cloud from the .pcd file.

        Returns:
            True if successful, False otherwise
        """
        try:
            print(f"Loading point cloud from {self.pcd_file}...")

            # Load point cloud using Open3D
            self.point_cloud = o3d.io.read_point_cloud(self.pcd_file)

            if len(self.point_cloud.points) == 0:
                print("Error: Point cloud is empty!")
                return False

            # Extract points and colors
            self.points = np.asarray(self.point_cloud.points, dtype=np.float32)

            # Handle colors
            if self.point_cloud.has_colors():
                self.colors = np.asarray(self.point_cloud.colors, dtype=np.float32)
                # Convert from [0,1] to [0,255] range
                self.colors = (self.colors * 255).astype(np.uint8)
            else:
                # Create default white colors if no colors available
                self.colors = np.full((len(self.points), 3), 255, dtype=np.uint8)

            print(f"Loaded {len(self.points)} points")
            print(f"Point cloud bounds:")
            print(f"  X: [{self.points[:, 0].min():.2f}, {self.points[:, 0].max():.2f}]")
            print(f"  Y: [{self.points[:, 1].min():.2f}, {self.points[:, 1].max():.2f}]")
            print(f"  Z: [{self.points[:, 2].min():.2f}, {self.points[:, 2].max():.2f}]")

            return True

        except Exception as e:
            print(f"Error loading point cloud: {e}")
            return False

    def get_camera_matrix(self) -> np.ndarray:
        """
        Get the camera transformation matrix (world to camera coordinates).

        Returns:
            4x4 transformation matrix
        """
        # Create transformation matrix from camera position and rotation
        transform = np.eye(4, dtype=np.float32)
        transform[:3, :3] = self.camera_rotation.T  # Transpose for world-to-camera
        transform[:3, 3] = -self.camera_rotation.T @ self.camera_position
        return transform

    def project_points(self) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Project 3D points to 2D image coordinates.

        Returns:
            Tuple of (projected_points, depths, valid_mask)
        """
        # Transform points to camera coordinates
        camera_matrix = self.get_camera_matrix()

        # Convert to homogeneous coordinates
        points_homo = np.hstack([self.points, np.ones((len(self.points), 1))])

        # Transform to camera coordinates
        points_cam = (camera_matrix @ points_homo.T).T

        # Extract 3D camera coordinates
        points_3d_cam = points_cam[:, :3]

        # Filter points behind the camera (negative Z)
        valid_mask = points_3d_cam[:, 2] > 0.01  # Small epsilon to avoid division by zero

        if not np.any(valid_mask):
            return np.array([]), np.array([]), valid_mask

        # Project to image coordinates using camera intrinsics
        points_2d = self.K @ points_3d_cam[valid_mask].T

        # Convert from homogeneous coordinates
        points_2d = points_2d[:2] / points_2d[2]
        points_2d = points_2d.T

        # Get depths for valid points
        depths = points_3d_cam[valid_mask, 2]

        return points_2d, depths, valid_mask

    def render_frame(self) -> np.ndarray:
        """
        Render the current camera view.

        Returns:
            Rendered image as numpy array
        """
        # Create blank image
        image = np.zeros((self.window_height, self.window_width, 3), dtype=np.uint8)

        # Project points to 2D
        points_2d, depths, valid_mask = self.project_points()

        if len(points_2d) == 0:
            # No visible points, return black image with text
            cv2.putText(image, "No visible points", (50, 50),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            return image

        # Filter points within image bounds
        in_bounds = ((points_2d[:, 0] >= 0) & (points_2d[:, 0] < self.window_width) &
                    (points_2d[:, 1] >= 0) & (points_2d[:, 1] < self.window_height))

        if not np.any(in_bounds):
            cv2.putText(image, "Points outside view", (50, 50),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            return image

        # Get valid points and colors
        valid_points_2d = points_2d[in_bounds]
        valid_colors = self.colors[valid_mask][in_bounds]
        valid_depths = depths[in_bounds]

        # Sort by depth (far to near) for proper rendering
        depth_order = np.argsort(-valid_depths)  # Negative for far-to-near

        # Render points
        for i in depth_order:
            x, y = int(valid_points_2d[i, 0]), int(valid_points_2d[i, 1])
            color = tuple(map(int, valid_colors[i]))

            # Draw point (small circle for better visibility)
            cv2.circle(image, (x, y), 1, color, -1)

        # Add status information
        self.draw_status_info(image)

        return image

    def draw_status_info(self, image: np.ndarray):
        """Draw camera position and control information on the image."""
        # Camera position
        pos_text = f"Pos: ({self.camera_position[0]:.1f}, {self.camera_position[1]:.1f}, {self.camera_position[2]:.1f})"
        cv2.putText(image, pos_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)

        # Controls reminder (bottom of screen)
        controls = [
            "WASD: Move | 1/2: Pitch | 3/4: Yaw | 5/6: Roll | ESC/Q: Quit"
        ]

        y_offset = self.window_height - 20
        for text in controls:
            cv2.putText(image, text, (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
            y_offset -= 20

    def handle_keyboard_input(self, key: int) -> bool:
        """
        Handle keyboard input for camera movement and rotation.

        Args:
            key: Key code from cv2.waitKey()

        Returns:
            False if should quit, True otherwise
        """
        # Quit keys
        if key == 27 or key == ord('q') or key == ord('Q'):  # ESC or Q
            return False

        # Movement keys (WASD)
        if key == ord('w') or key == ord('W'):
            # Move forward (negative Z in camera coordinates)
            forward = self.camera_rotation @ np.array([0, 0, -self.move_speed])
            self.camera_position += forward

        elif key == ord('s') or key == ord('S'):
            # Move backward (positive Z in camera coordinates)
            backward = self.camera_rotation @ np.array([0, 0, self.move_speed])
            self.camera_position += backward

        elif key == ord('a') or key == ord('A'):
            # Move left (negative X in camera coordinates)
            left = self.camera_rotation @ np.array([-self.move_speed, 0, 0])
            self.camera_position += left

        elif key == ord('d') or key == ord('D'):
            # Move right (positive X in camera coordinates)
            right = self.camera_rotation @ np.array([self.move_speed, 0, 0])
            self.camera_position += right

        # Rotation keys (1-6)
        elif key == ord('1'):
            # Rotate up (positive rotation around X-axis)
            self.rotate_camera('x', self.rotation_speed)

        elif key == ord('2'):
            # Rotate down (negative rotation around X-axis)
            self.rotate_camera('x', -self.rotation_speed)

        elif key == ord('3'):
            # Rotate left (positive rotation around Y-axis)
            self.rotate_camera('y', self.rotation_speed)

        elif key == ord('4'):
            # Rotate right (negative rotation around Y-axis)
            self.rotate_camera('y', -self.rotation_speed)

        elif key == ord('5'):
            # Roll left (positive rotation around Z-axis)
            self.rotate_camera('z', self.rotation_speed)

        elif key == ord('6'):
            # Roll right (negative rotation around Z-axis)
            self.rotate_camera('z', -self.rotation_speed)

        return True

    def rotate_camera(self, axis: str, angle: float):
        """
        Rotate the camera around the specified axis.

        Args:
            axis: 'x', 'y', or 'z'
            angle: Rotation angle in radians
        """
        if axis == 'x':
            rotation_matrix = np.array([
                [1, 0, 0],
                [0, np.cos(angle), -np.sin(angle)],
                [0, np.sin(angle), np.cos(angle)]
            ])
        elif axis == 'y':
            rotation_matrix = np.array([
                [np.cos(angle), 0, np.sin(angle)],
                [0, 1, 0],
                [-np.sin(angle), 0, np.cos(angle)]
            ])
        elif axis == 'z':
            rotation_matrix = np.array([
                [np.cos(angle), -np.sin(angle), 0],
                [np.sin(angle), np.cos(angle), 0],
                [0, 0, 1]
            ])
        else:
            return

        # Apply rotation to current camera rotation
        self.camera_rotation = self.camera_rotation @ rotation_matrix

    def run(self):
        """Main navigation loop."""
        if not self.load_point_cloud():
            print("Failed to load point cloud. Exiting.")
            return

        # Create display window
        cv2.namedWindow(self.window_name, cv2.WINDOW_AUTOSIZE)

        print("\nStarting navigation...")
        print("Use WASD to move, 1-6 for rotation, ESC/Q to quit")

        # Main loop
        frame_count = 0
        start_time = time.time()

        try:
            while True:
                # Render current frame
                frame = self.render_frame()

                # Display frame
                cv2.imshow(self.window_name, frame)

                # Handle keyboard input (1ms timeout for responsiveness)
                key = cv2.waitKey(1) & 0xFF
                if key != 255:  # Key was pressed
                    if not self.handle_keyboard_input(key):
                        break

                # Calculate and display FPS occasionally
                frame_count += 1
                if frame_count % 30 == 0:
                    elapsed = time.time() - start_time
                    fps = frame_count / elapsed
                    print(f"FPS: {fps:.1f}")

        except KeyboardInterrupt:
            print("\nInterrupted by user")

        finally:
            cv2.destroyAllWindows()
            print("Navigation ended.")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Point Cloud Navigator")
    parser.add_argument("pcd_file", nargs='?', default="all_raw_points.pcd",
                       help="Path to the .pcd file (default: all_raw_points.pcd)")
    parser.add_argument("--width", type=int, default=800,
                       help="Window width (default: 800)")
    parser.add_argument("--height", type=int, default=600,
                       help="Window height (default: 600)")
    parser.add_argument("--move-speed", type=float, default=0.5,
                       help="Movement speed (default: 0.5)")
    parser.add_argument("--rotation-speed", type=float, default=0.1,
                       help="Rotation speed in radians (default: 0.1)")

    args = parser.parse_args()

    # Check if file exists
    import os
    if not os.path.exists(args.pcd_file):
        print(f"Error: Point cloud file '{args.pcd_file}' not found!")
        print("Please provide a valid .pcd file path.")
        sys.exit(1)

    # Create and run navigator
    navigator = PointCloudNavigator(
        pcd_file=args.pcd_file,
        window_width=args.width,
        window_height=args.height
    )

    # Set custom speeds if provided
    navigator.move_speed = args.move_speed
    navigator.rotation_speed = args.rotation_speed

    # Run the navigation
    navigator.run()


if __name__ == "__main__":
    main()