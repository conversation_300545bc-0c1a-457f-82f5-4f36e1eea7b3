# Point Cloud Navigator

A real-time point cloud viewer with interactive camera navigation controls. This script loads dense RGB point clouds (.pcd format) and provides smooth navigation through the 3D scene using keyboard controls.

## Features

- **Real-time Navigation**: Smooth camera movement and rotation controls
- **RGB Point Cloud Support**: Loads and displays colored point clouds from .pcd files
- **3D-to-2D Projection**: Uses camera intrinsics to project 3D points to 2D screen coordinates
- **Efficient Rendering**: Handles large point clouds (tested with 18M+ points)
- **Customizable Parameters**: Adjustable movement speed, rotation speed, and window size

## Requirements

- Python 3.7+
- OpenCV (`opencv-python`)
- Open3D (`open3d`)
- NumPy (`numpy`)

## Installation

Install the required dependencies:

```bash
pip install opencv-python open3d numpy
```

## Usage

### Basic Usage

Run with the default point cloud file:
```bash
python nvs.py
```

### Custom Point Cloud File

Specify a different .pcd file:
```bash
python nvs.py path/to/your/pointcloud.pcd
```

### Advanced Options

```bash
python nvs.py --width 1024 --height 768 --move-speed 1.0 --rotation-speed 0.2
```

## Controls

### Movement (WASD)
- **W**: Move forward
- **A**: Move left
- **S**: Move backward
- **D**: Move right

### Rotation (Numeric Keys)
- **1**: Rotate up (pitch up)
- **2**: Rotate down (pitch down)
- **3**: Rotate left (yaw left)
- **4**: Rotate right (yaw right)
- **5**: Roll left
- **6**: Roll right

### Exit
- **ESC** or **Q**: Quit the application

## Camera Parameters

The script uses predefined camera intrinsic parameters:
- **Focal Length**: fx = fy = 800.0
- **Principal Point**: cx = window_width/2, cy = window_height/2

These can be modified in the script if you have specific camera calibration data.

## Command Line Options

- `pcd_file`: Path to the .pcd file (default: all_raw_points.pcd)
- `--width`: Window width in pixels (default: 800)
- `--height`: Window height in pixels (default: 600)
- `--move-speed`: Movement speed per key press (default: 0.5)
- `--rotation-speed`: Rotation speed in radians per key press (default: 0.1)

## Technical Details

### Point Cloud Loading
- Supports binary and ASCII .pcd files
- Automatically handles RGB color information
- Falls back to white color if no color data is available

### Rendering Pipeline
1. **Transform**: Apply camera transformation (position + rotation)
2. **Project**: Use camera intrinsics to project 3D points to 2D
3. **Filter**: Remove points behind camera and outside view frustum
4. **Sort**: Depth-sort points for proper rendering
5. **Draw**: Render points as small circles with original colors

### Performance
- Efficient point filtering and projection
- Real-time rendering suitable for navigation
- FPS counter displayed in console

## Example Output

When running, you'll see:
- Real-time rendered view of the point cloud
- Camera position displayed on screen
- Control reminders at the bottom
- FPS information in the console

## Troubleshooting

### "No visible points" message
- Camera might be positioned where no points are visible
- Try moving around with WASD keys
- Check if the point cloud file loaded correctly

### Low performance
- Reduce window size with `--width` and `--height`
- The script handles large point clouds but performance depends on your hardware

### Point cloud not loading
- Verify the .pcd file path is correct
- Ensure the file is a valid PCD format
- Check console output for error messages

## File Structure

```
.
├── nvs.py              # Main navigation script
├── README.md           # This documentation
└── all_raw_points.pcd  # Example point cloud file
```

## License

This script is provided as-is for educational and research purposes.
