#!/usr/bin/env python3
"""
Test script for Point Cloud Navigator

This script tests the core functionality of the navigation system
without requiring a display, making it suitable for headless environments.
"""

import numpy as np
import sys
import os

# Add the current directory to the path to import nvs
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from nvs import PointCloudNavigator

def test_point_cloud_loading():
    """Test point cloud loading functionality."""
    print("Testing point cloud loading...")
    
    navigator = PointCloudNavigator("all_raw_points.pcd")
    
    # Test loading
    success = navigator.load_point_cloud()
    if not success:
        print("❌ Failed to load point cloud")
        return False
    
    print(f"✅ Successfully loaded {len(navigator.points)} points")
    print(f"✅ Point cloud has colors: {navigator.colors is not None}")
    
    return True

def test_camera_transformations():
    """Test camera transformation matrices."""
    print("\nTesting camera transformations...")
    
    navigator = PointCloudNavigator("all_raw_points.pcd")
    
    # Test initial camera matrix
    initial_matrix = navigator.get_camera_matrix()
    expected_shape = (4, 4)
    
    if initial_matrix.shape != expected_shape:
        print(f"❌ Camera matrix has wrong shape: {initial_matrix.shape}, expected {expected_shape}")
        return False
    
    print("✅ Camera matrix has correct shape")
    
    # Test camera movement
    initial_pos = navigator.camera_position.copy()
    navigator.camera_position += np.array([1.0, 0.0, 0.0])
    
    new_matrix = navigator.get_camera_matrix()
    if np.allclose(initial_matrix, new_matrix):
        print("❌ Camera matrix didn't change after position update")
        return False
    
    print("✅ Camera matrix updates correctly with position changes")
    
    # Test camera rotation
    navigator.rotate_camera('y', 0.1)
    rotated_matrix = navigator.get_camera_matrix()
    
    if np.allclose(new_matrix, rotated_matrix):
        print("❌ Camera matrix didn't change after rotation")
        return False
    
    print("✅ Camera matrix updates correctly with rotation")
    
    return True

def test_point_projection():
    """Test 3D to 2D point projection."""
    print("\nTesting point projection...")
    
    navigator = PointCloudNavigator("all_raw_points.pcd")
    
    if not navigator.load_point_cloud():
        print("❌ Failed to load point cloud for projection test")
        return False
    
    # Test projection with a subset of points for speed
    original_points = navigator.points
    navigator.points = navigator.points[:1000]  # Use first 1000 points
    navigator.colors = navigator.colors[:1000]
    
    try:
        points_2d, depths, valid_mask = navigator.project_points()
        
        if len(points_2d) == 0:
            print("⚠️  No points projected (camera might be in empty space)")
        else:
            print(f"✅ Successfully projected {len(points_2d)} points")
            print(f"✅ Depth range: [{depths.min():.2f}, {depths.max():.2f}]")
        
        # Test that valid_mask has correct length
        if len(valid_mask) != len(navigator.points):
            print(f"❌ Valid mask length mismatch: {len(valid_mask)} vs {len(navigator.points)}")
            return False
        
        print("✅ Valid mask has correct length")
        
    except Exception as e:
        print(f"❌ Point projection failed: {e}")
        return False
    finally:
        # Restore original points
        navigator.points = original_points
    
    return True

def test_keyboard_input_handling():
    """Test keyboard input handling."""
    print("\nTesting keyboard input handling...")
    
    navigator = PointCloudNavigator("all_raw_points.pcd")
    
    # Test movement keys
    initial_pos = navigator.camera_position.copy()
    
    # Test W key (forward movement)
    result = navigator.handle_keyboard_input(ord('w'))
    if not result:
        print("❌ W key should not quit the application")
        return False
    
    if np.allclose(navigator.camera_position, initial_pos):
        print("❌ W key didn't move camera")
        return False
    
    print("✅ W key moves camera forward")
    
    # Test rotation keys
    initial_rotation = navigator.camera_rotation.copy()
    navigator.handle_keyboard_input(ord('1'))
    
    if np.allclose(navigator.camera_rotation, initial_rotation):
        print("❌ Rotation key didn't rotate camera")
        return False
    
    print("✅ Rotation keys work correctly")
    
    # Test quit keys
    quit_result = navigator.handle_keyboard_input(27)  # ESC key
    if quit_result:
        print("❌ ESC key should return False to quit")
        return False
    
    print("✅ ESC key correctly signals quit")
    
    return True

def test_render_frame():
    """Test frame rendering (without display)."""
    print("\nTesting frame rendering...")
    
    navigator = PointCloudNavigator("all_raw_points.pcd", 400, 300)  # Smaller for faster testing
    
    if not navigator.load_point_cloud():
        print("❌ Failed to load point cloud for render test")
        return False
    
    # Use subset for faster rendering
    navigator.points = navigator.points[:5000]
    navigator.colors = navigator.colors[:5000]
    
    try:
        frame = navigator.render_frame()
        
        expected_shape = (300, 400, 3)  # height, width, channels
        if frame.shape != expected_shape:
            print(f"❌ Frame has wrong shape: {frame.shape}, expected {expected_shape}")
            return False
        
        print("✅ Frame rendered with correct dimensions")
        
        # Check if frame is not completely black (should have some content)
        if frame.max() == 0:
            print("⚠️  Frame is completely black (might be normal if no points visible)")
        else:
            print("✅ Frame contains rendered content")
        
    except Exception as e:
        print(f"❌ Frame rendering failed: {e}")
        return False
    
    return True

def main():
    """Run all tests."""
    print("🧪 Running Point Cloud Navigator Tests\n")
    
    tests = [
        test_point_cloud_loading,
        test_camera_transformations,
        test_point_projection,
        test_keyboard_input_handling,
        test_render_frame,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("❌ Test failed")
        except Exception as e:
            print(f"❌ Test crashed: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The navigation system is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
