#!/usr/bin/env python3
"""
Example usage of the Point Cloud Navigator

This script demonstrates how to use the navigation system programmatically
and shows different ways to interact with the point cloud viewer.
"""

import sys
import os
import time

# Add the current directory to the path to import nvs
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from nvs import PointCloudNavigator

def example_basic_usage():
    """Basic usage example."""
    print("🚀 Basic Usage Example")
    print("=" * 50)
    
    # Create navigator with default settings
    navigator = PointCloudNavigator("sample_medium.pcd")
    
    # Load the point cloud
    if not navigator.load_point_cloud():
        print("❌ Failed to load point cloud")
        return
    
    print("✅ Point cloud loaded successfully!")
    print(f"   Points: {len(navigator.points)}")
    print(f"   Colors: {'Yes' if navigator.colors is not None else 'No'}")
    
    # Test basic operations
    print("\n📍 Initial camera position:", navigator.camera_position)
    
    # Move camera
    navigator.camera_position += [5, 0, 0]
    print("📍 After moving right:", navigator.camera_position)
    
    # Rotate camera
    navigator.rotate_camera('y', 0.5)  # 0.5 radians ≈ 28.6 degrees
    print("🔄 Camera rotated around Y-axis")
    
    # Test projection
    points_2d, depths, valid_mask = navigator.project_points()
    print(f"🎯 Projected {len(points_2d)} visible points")
    
    if len(points_2d) > 0:
        print(f"   Depth range: [{depths.min():.2f}, {depths.max():.2f}]")

def example_custom_settings():
    """Example with custom settings."""
    print("\n🎛️  Custom Settings Example")
    print("=" * 50)
    
    # Create navigator with custom settings
    navigator = PointCloudNavigator(
        pcd_file="sample_small.pcd",
        window_width=1024,
        window_height=768
    )
    
    # Customize movement and rotation speeds
    navigator.move_speed = 2.0  # Faster movement
    navigator.rotation_speed = 0.2  # Faster rotation
    
    # Custom camera intrinsics (if you have calibration data)
    navigator.fx = 1000.0
    navigator.fy = 1000.0
    navigator.cx = 512.0
    navigator.cy = 384.0
    
    # Update the intrinsic matrix
    navigator.K = [[navigator.fx, 0, navigator.cx],
                   [0, navigator.fy, navigator.cy],
                   [0, 0, 1]]
    
    print("✅ Navigator created with custom settings")
    print(f"   Window size: {navigator.window_width}x{navigator.window_height}")
    print(f"   Move speed: {navigator.move_speed}")
    print(f"   Rotation speed: {navigator.rotation_speed}")
    print(f"   Focal length: fx={navigator.fx}, fy={navigator.fy}")

def example_programmatic_navigation():
    """Example of programmatic navigation (automated camera movement)."""
    print("\n🤖 Programmatic Navigation Example")
    print("=" * 50)
    
    navigator = PointCloudNavigator("sample_medium.pcd")
    
    if not navigator.load_point_cloud():
        print("❌ Failed to load point cloud")
        return
    
    # Define a camera path
    waypoints = [
        ([0, 0, 0], 'start'),
        ([10, 0, 0], 'move right'),
        ([10, 10, 0], 'move forward'),
        ([0, 10, 0], 'move left'),
        ([0, 0, 0], 'return to start'),
    ]
    
    print("🛤️  Following camera path:")
    
    for i, (position, description) in enumerate(waypoints):
        navigator.camera_position = position
        
        # Test rendering at each waypoint
        frame = navigator.render_frame()
        points_2d, depths, valid_mask = navigator.project_points()
        
        print(f"   {i+1}. {description}: pos={position}, visible_points={len(points_2d)}")
        
        # Simulate some processing time
        time.sleep(0.1)
    
    print("✅ Camera path completed!")

def example_keyboard_simulation():
    """Example of simulating keyboard input."""
    print("\n⌨️  Keyboard Simulation Example")
    print("=" * 50)
    
    navigator = PointCloudNavigator("sample_small.pcd")
    
    # Simulate key presses
    key_sequence = [
        (ord('w'), "Move forward"),
        (ord('w'), "Move forward again"),
        (ord('d'), "Move right"),
        (ord('1'), "Rotate up"),
        (ord('3'), "Rotate left"),
        (ord('s'), "Move backward"),
    ]
    
    print("🎮 Simulating key presses:")
    
    for key, description in key_sequence:
        old_pos = navigator.camera_position.copy()
        old_rot = navigator.camera_rotation.copy()
        
        # Handle the key press
        continue_running = navigator.handle_keyboard_input(key)
        
        # Check what changed
        pos_changed = not all(old_pos == navigator.camera_position)
        rot_changed = not (old_rot == navigator.camera_rotation).all()
        
        change_type = []
        if pos_changed:
            change_type.append("position")
        if rot_changed:
            change_type.append("rotation")
        
        change_str = " & ".join(change_type) if change_type else "nothing"
        
        print(f"   Key '{chr(key)}' ({description}): changed {change_str}")
        
        if not continue_running:
            print("   🛑 Quit key pressed, stopping simulation")
            break
    
    print("✅ Keyboard simulation completed!")

def main():
    """Run all examples."""
    print("📚 Point Cloud Navigator Usage Examples\n")
    
    # Check if sample files exist
    sample_files = ["sample_small.pcd", "sample_medium.pcd"]
    missing_files = [f for f in sample_files if not os.path.exists(f)]
    
    if missing_files:
        print("⚠️  Some sample files are missing. Creating them now...")
        os.system("python create_sample_pcd.py")
        print()
    
    try:
        example_basic_usage()
        example_custom_settings()
        example_programmatic_navigation()
        example_keyboard_simulation()
        
        print("\n🎉 All examples completed successfully!")
        print("\n💡 To run the interactive navigator, use:")
        print("   python nvs.py sample_medium.pcd")
        
    except Exception as e:
        print(f"\n❌ Example failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
